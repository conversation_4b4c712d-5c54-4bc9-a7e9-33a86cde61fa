package libUtils

import (
	"bytes"
	"encoding/csv"
	"golang.org/x/text/encoding/simplifiedchinese"
	"golang.org/x/text/transform"
)

// CSVEncodingType CSV编码类型
type CSVEncodingType int

const (
	// UTF8WithBOM UTF-8 编码带 BOM
	UTF8WithBOM CSVEncodingType = iota
	// UTF8WithoutBOM UTF-8 编码不带 BOM
	UTF8WithoutBOM
	// GBK GBK 编码（兼容老版本 Excel）
	GBK
)

// CSVHelper CSV 导出助手
type CSVHelper struct {
	encoding CSVEncodingType
}

// NewCSVHelper 创建 CSV 助手
func NewCSVHelper(encoding CSVEncodingType) *CSVHelper {
	return &CSVHelper{encoding: encoding}
}

// WriteCSV 写入 CSV 数据
func (h *CSVHelper) WriteCSV(records [][]string) ([]byte, error) {
	var buf bytes.Buffer

	switch h.encoding {
	case UTF8WithBOM:
		// 添加 UTF-8 BOM
		buf.Write([]byte{0xEF, 0xBB, 0xBF})
		return h.writeUTF8CSV(&buf, records)

	case UTF8WithoutBOM:
		return h.writeUTF8CSV(&buf, records)

	case GBK:
		return h.writeGBKCSV(records)

	default:
		// 默认使用 UTF-8 with BOM
		buf.Write([]byte{0xEF, 0xBB, 0xBF})
		return h.writeUTF8CSV(&buf, records)
	}
}

// writeUTF8CSV 写入 UTF-8 编码的 CSV
func (h *CSVHelper) writeUTF8CSV(buf *bytes.Buffer, records [][]string) ([]byte, error) {
	csvWriter := csv.NewWriter(buf)
	csvWriter.Comma = ','

	for _, record := range records {
		if err := csvWriter.Write(record); err != nil {
			return nil, err
		}
	}
	csvWriter.Flush()

	if err := csvWriter.Error(); err != nil {
		return nil, err
	}

	return buf.Bytes(), nil
}

// writeGBKCSV 写入 GBK 编码的 CSV
func (h *CSVHelper) writeGBKCSV(records [][]string) ([]byte, error) {
	var buf bytes.Buffer
	csvWriter := csv.NewWriter(&buf)
	csvWriter.Comma = ','

	for _, record := range records {
		if err := csvWriter.Write(record); err != nil {
			return nil, err
		}
	}
	csvWriter.Flush()

	if err := csvWriter.Error(); err != nil {
		return nil, err
	}

	// 将 UTF-8 转换为 GBK
	encoder := simplifiedchinese.GBK.NewEncoder()
	gbkData, err := encoder.Bytes(buf.Bytes())
	if err != nil {
		return nil, err
	}

	return gbkData, nil
}

// GetContentType 获取对应的 Content-Type
func (h *CSVHelper) GetContentType() string {
	switch h.encoding {
	case GBK:
		return "text/csv; charset=gbk"
	default:
		return "text/csv; charset=utf-8"
	}
}

// ConvertToGBK 将 UTF-8 字符串转换为 GBK 字节
func ConvertToGBK(utf8Str string) ([]byte, error) {
	encoder := simplifiedchinese.GBK.NewEncoder()
	reader := transform.NewReader(bytes.NewReader([]byte(utf8Str)), encoder)
	gbkBytes := make([]byte, len(utf8Str)*2) // GBK 最多是 UTF-8 的两倍长度
	n, err := reader.Read(gbkBytes)
	if err != nil {
		return nil, err
	}
	return gbkBytes[:n], nil
}
